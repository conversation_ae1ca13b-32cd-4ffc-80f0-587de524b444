<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org" th:fragment="layout (title, content, scripts)">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title th:replace="${title}">JPA Auditing Demo</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <style>
        .audit-badge {
            font-size: 0.75rem;
        }
        .audit-timeline {
            position: relative;
            padding-left: 30px;
        }
        .audit-timeline::before {
            content: '';
            position: absolute;
            left: 15px;
            top: 0;
            bottom: 0;
            width: 2px;
            background: #dee2e6;
        }
        .audit-timeline-item {
            position: relative;
            margin-bottom: 20px;
        }
        .audit-timeline-item::before {
            content: '';
            position: absolute;
            left: -23px;
            top: 8px;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #007bff;
            border: 2px solid #fff;
            box-shadow: 0 0 0 2px #dee2e6;
        }
        .audit-timeline-item.create::before { background: #28a745; }
        .audit-timeline-item.update::before { background: #ffc107; }
        .audit-timeline-item.delete::before { background: #dc3545; }

        .field-change {
            background: #f8f9fa;
            border-left: 4px solid #007bff;
            padding: 10px;
            margin: 5px 0;
        }
        .field-change.sensitive {
            border-left-color: #dc3545;
            background: #fff5f5;
        }

        .navbar-brand {
            font-weight: bold;
        }

        .sidebar {
            min-height: calc(100vh - 56px);
            background: #f8f9fa;
        }

        .main-content {
            min-height: calc(100vh - 56px);
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" th:href="@{/}">
                <i class="fas fa-history me-2"></i>
                JPA Auditing Demo
            </a>

            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>

            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" th:href="@{/}">
                            <i class="fas fa-home me-1"></i>Home
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" th:href="@{/users}">
                            <i class="fas fa-users me-1"></i>Users
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" th:href="@{/audit}">
                            <i class="fas fa-clipboard-list me-1"></i>Audit Dashboard
                        </a>
                    </li>
                </ul>

                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user me-1"></i>Demo User
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#"><i class="fas fa-cog me-1"></i>Settings</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="#"><i class="fas fa-sign-out-alt me-1"></i>Logout</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar (optional) -->
            <div th:if="${showSidebar}" class="col-md-3 col-lg-2 sidebar p-3">
                <div th:fragment="sidebar">
                    <h6 class="text-muted mb-3">NAVIGATION</h6>
                    <ul class="nav nav-pills flex-column">
                        <li class="nav-item">
                            <a class="nav-link" th:href="@{/}">
                                <i class="fas fa-home me-2"></i>Dashboard
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" th:href="@{/users}">
                                <i class="fas fa-users me-2"></i>Users
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" th:href="@{/audit}">
                                <i class="fas fa-clipboard-list me-2"></i>Audit Logs
                            </a>
                        </li>
                    </ul>
                </div>
            </div>

            <!-- Main Content Area -->
            <div th:class="${showSidebar} ? 'col-md-9 col-lg-10 main-content p-4' : 'col-12 main-content p-4'">
                <!-- Alerts -->
                <div th:if="${success}" class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="fas fa-check-circle me-2"></i>
                    <span th:text="${success}">Success message</span>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>

                <div th:if="${error}" class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="fas fa-exclamation-circle me-2"></i>
                    <span th:text="${error}">Error message</span>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>

                <!-- Page Content -->
                <div th:replace="${content}">
                    <!-- Content will be replaced here -->
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Custom JavaScript -->
    <script th:inline="javascript">
        // Global audit functions
        window.AuditDemo = {
            // Show audit trail for an entity
            showAuditTrail: function(entityName, entityId) {
                window.location.href = '/audit-demo/audit/' + entityName + '/' + entityId;
            },

            // Format timestamp
            formatTimestamp: function(timestamp) {
                return new Date(timestamp).toLocaleString();
            },

            // Get operation badge class
            getOperationBadgeClass: function(operation) {
                switch(operation) {
                    case 'CREATE': return 'bg-success';
                    case 'UPDATE': return 'bg-warning';
                    case 'DELETE': return 'bg-danger';
                    default: return 'bg-secondary';
                }
            }
        };
    </script>

    <!-- Page-specific scripts -->
    <div th:replace="${scripts}">
        <!-- Scripts will be replaced here -->
    </div>
</body>
</html>
